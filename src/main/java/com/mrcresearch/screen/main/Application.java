package com.mrcresearch.screen.main;

import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatLightLaf;
import com.mrcresearch.screen.controler.ScreenControler;
import com.mrcresearch.screen.login.Login;
import com.mrcresearch.service.common.FontSettings;
import com.mrcresearch.service.common.Version;
import com.mrcresearch.util.database.DatabaseUtil;
import com.mrcresearch.util.database.SettingsRepository;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.plaf.FontUIResource;
import java.awt.*;
import java.util.Enumeration;
import java.util.Objects;

public class Application extends JFrame {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);
    ScreenControler controller;
    @Getter
    private Login login;
    @Getter
    private Main main;

    /**
     * コンストラクタ。
     */
    public Application() {
        init();
    }

    /**
     * メイン処理
     *
     * @param args
     */
    public static void main(String[] args) {
        try {
            // Initialize database to ensure settings table exists and migrations are applied
            DatabaseUtil.initDatabase();

            // Small delay to ensure database operations are complete
            Thread.sleep(100);

            // Perform seller data migration if needed
            com.mrcresearch.util.database.SellerMigrationUtil.checkAndMigrateIfNeeded();

            // Load theme setting from database
            String themeType = SettingsRepository.getThemeType();

            // Setup FlatLaf theme based on setting
            setupTheme(themeType);

            // Load font size setting
            FontSettings.loadFontSize();

            // アプリケーション全体のフォントを設定
            setupFont();

            EventQueue.invokeLater(() -> new Application().setVisible(true));
        } catch (Exception e) {
            logger.error("Error during application startup", e);
            // Fallback to default light theme setup
            setupTheme("light");
            setupFont();
            EventQueue.invokeLater(() -> new Application().setVisible(true));
        }
    }

    /**
     * Setup theme based on theme type
     *
     * @param themeType "light" or "dark"
     */
    private static void setupTheme(String themeType) {
        try {
            if ("dark".equals(themeType)) {
                // Setup dark theme
                FlatDarkLaf.setup();
                // Set button colors for dark theme
                setupDarkThemeButtons();
                // Set icon colors for dark theme
                setupDarkThemeIcons();
            } else {
                // Setup light theme
                FlatLightLaf.setup();
                // Set button colors for light theme
                setupLightThemeButtons();
                // Set icon colors for light theme
                setupLightThemeIcons();
            }
        } catch (Exception e) {
            logger.error("Error setting up theme", e);
            // Fallback to light theme
            FlatLightLaf.setup();
            setupLightThemeButtons();
            setupLightThemeIcons();
        }
    }

    /**
     * Setup button colors for dark theme
     */
    private static void setupDarkThemeButtons() {
        // Remove custom button background to use theme defaults
        UIManager.put("Button.background", null);
        UIManager.put("Button.focusedBackground", null);
        UIManager.put("Button.pressedBackground", null);
        UIManager.put("Button.selectedBackground", null);
        UIManager.put("Button.disabledBackground", null);
    }

    /**
     * Setup button colors for light theme
     */
    private static void setupLightThemeButtons() {
        // Set button background color for light theme
        UIManager.put("Button.background", new Color(244, 244, 244));
        UIManager.put("Button.focusedBackground", new Color(234, 234, 234));
        UIManager.put("Button.pressedBackground", new Color(224, 224, 224));
        UIManager.put("Button.selectedBackground", new Color(234, 234, 234));
        UIManager.put("Button.disabledBackground", new Color(250, 250, 250));
    }

    /**
     * Setup icon colors for dark theme
     */
    private static void setupDarkThemeIcons() {
        // Set default icon colors for dark theme
        UIManager.put("Component.iconColor", Color.WHITE);
        UIManager.put("Button.iconColor", Color.WHITE);
        UIManager.put("ToggleButton.iconColor", Color.WHITE);
    }

    /**
     * Setup icon colors for light theme
     */
    private static void setupLightThemeIcons() {
        // Set default icon colors for light theme
        UIManager.put("Component.iconColor", new Color(60, 60, 60));
        UIManager.put("Button.iconColor", new Color(60, 60, 60)); // Buttons typically have colored backgrounds
        UIManager.put("ToggleButton.iconColor", new Color(60, 60, 60));
    }

    /**
     * リソースからカスタムフォントを読み込み、アプリケーション全体に適用します。
     */
    private static void setupFont() {
        try {
            // FontSettingsからデフォルトフォントを取得
            Font customFont = FontSettings.getTextFont();

            // UIマネージャーにフォントを設定
            setUIFont(new FontUIResource(customFont));

        } catch (Exception e) {
            logger.error("カスタムフォントの読み込みに失敗しました。デフォルトフォントを使用します。", e);
        }
    }

    /**
     * Apply theme change immediately without restart
     *
     * @param themeType "light" or "dark"
     */
    public static void applyThemeChange(String themeType) {
        try {
            // Setup the new theme
            setupTheme(themeType);

            // Small delay to ensure theme is fully applied
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // Update all existing UI components on EDT
            SwingUtilities.invokeLater(() -> {
                try {
                    // Update all windows
                    for (Window window : Window.getWindows()) {
                        SwingUtilities.updateComponentTreeUI(window);

                        // If this is an Application window, also update the Main panel specifically
                        if (window instanceof Application app) {
                            if (app.main != null) {
                                app.main.applyThemeChange();
                            }
                        }

                        window.revalidate();
                        window.repaint();
                    }

                    logger.info("テーマが以下に変更されました: {}", themeType);
                } catch (Exception e) {
                    logger.error("Error in EDT theme update: {}", e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            logger.error("Error applying theme change: {}", e.getMessage(), e);
        }
    }

    /**
     * Apply font change immediately without restart
     */
    public static void applyFontChange() {
        try {
            // Re-setup the font with the new size
            setupFont();

            // Update all existing UI components on EDT
            SwingUtilities.invokeLater(() -> {
                try {
                    // Update all windows
                    for (Window window : Window.getWindows()) {
                        SwingUtilities.updateComponentTreeUI(window);
                        window.revalidate();
                        window.repaint();
                    }

                    logger.info("フォントサイズが以下に変更されました: {}", FontSettings.getCurrentFontSize());
                } catch (Exception e) {
                    logger.error("Error in EDT font update: {}", e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            logger.error("Error applying font change: {}", e.getMessage(), e);
        }
    }

    /**
     * Sets the default font for all Swing components
     *
     * @param font The font to use
     */
    private static void setUIFont(FontUIResource font) {
        Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }

    /**
     * 初期化
     */
    private void init() {
        setTLSVersion();

        // 画面操作系はこっち
        controller = new ScreenControler(this);

        // フレーム全体の設定
        setTitle("MrcResearch Ver:" + Version.THIS);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1280, 720);
        setExtendedState(JFrame.MAXIMIZED_BOTH);
        setLocationRelativeTo(null);

        ImageIcon icon = new ImageIcon(Objects.requireNonNull(getClass().getResource("/img/icon.png")));
        setIconImage(icon.getImage());

        // ログイン画面
        login = new Login();
        setContentPane(login);
        controller.addLoginListener(login.getBtnLogin());

        // メイン画面
        main = new Main();
        main.setVisible(true); // Ensure the main panel is visible
    }

    /**
     * TLSバージョンを明示的に設定
     */
    private void setTLSVersion() {
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
            logger.info("TLSバージョンの設定が完了しました");
        } catch (Exception e) {
            logger.error("TLSバージョンの設定中にエラーが発生しました: {}", e.getMessage());
        }
    }

}