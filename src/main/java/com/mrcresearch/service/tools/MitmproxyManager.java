package com.mrcresearch.service.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * mitmproxyプロセスを管理し、Javaアプリケーションとの統合を提供するクラス
 * BrowserUp Proxyの代替として、mitmproxyを使用してネットワークトラフィックを監視・記録する
 */
public class MitmproxyManager {
    private static final Logger logger = LoggerFactory.getLogger(MitmproxyManager.class);
    
    private Process mitmproxyProcess;
    private int proxyPort = 8888;
    private Path tempDir;
    private Path flowFile;
    private Path addonFile;
    private boolean isRunning = false;
    
    /**
     * mitmproxyマネージャーを初期化
     * 
     * @param port プロキシポート番号
     */
    public MitmproxyManager(int port) {
        this.proxyPort = port;
        try {
            // 一時ディレクトリを作成
            this.tempDir = Files.createTempDirectory("mitmproxy_");
            this.flowFile = tempDir.resolve("flows.mitm");
            this.addonFile = tempDir.resolve("addon.py");
            
            // カスタムアドオンを作成
            createCustomAddon();
            
            logger.info("MitmproxyManager initialized with port: {}", port);
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize MitmproxyManager", e);
        }
    }
    
    /**
     * mitmproxyプロセスを開始
     */
    public void start() {
        if (isRunning) {
            logger.warn("Mitmproxy is already running");
            return;
        }
        
        try {
            // mitmproxyコマンドを構築
            List<String> command = buildMitmproxyCommand();
            
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.directory(tempDir.toFile());
            
            // プロセスを開始
            mitmproxyProcess = processBuilder.start();
            
            // プロセスの出力を監視（別スレッドで）
            startOutputMonitoring();
            
            // プロキシが起動するまで少し待機
            Thread.sleep(2000);
            
            isRunning = true;
            logger.info("Mitmproxy started on port: {}", proxyPort);
            
        } catch (Exception e) {
            logger.error("Failed to start mitmproxy", e);
            throw new RuntimeException("Failed to start mitmproxy", e);
        }
    }
    
    /**
     * mitmproxyプロセスを停止
     */
    public void stop() {
        if (!isRunning || mitmproxyProcess == null) {
            return;
        }
        
        try {
            // プロセスを終了
            mitmproxyProcess.destroy();
            
            // 強制終了が必要な場合
            if (!mitmproxyProcess.waitFor(5, TimeUnit.SECONDS)) {
                mitmproxyProcess.destroyForcibly();
                mitmproxyProcess.waitFor(5, TimeUnit.SECONDS);
            }
            
            isRunning = false;
            logger.info("Mitmproxy stopped");
            
        } catch (InterruptedException e) {
            logger.error("Error stopping mitmproxy", e);
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 記録されたフローデータを取得
     * 
     * @return フローデータのJSONリスト
     */
    public List<JsonNode> getFlows() {
        List<JsonNode> flows = new ArrayList<>();
        
        if (!Files.exists(flowFile)) {
            logger.debug("Flow file does not exist: {}", flowFile);
            return flows;
        }
        
        try {
            // mitmproxyのフローファイルを読み込み
            ProcessBuilder pb = new ProcessBuilder("mitmdump", "-r", flowFile.toString(), "--set", "confdir=" + tempDir.toString());
            Process process = pb.start();
            
            ObjectMapper mapper = new ObjectMapper();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    try {
                        JsonNode flow = mapper.readTree(line);
                        flows.add(flow);
                    } catch (Exception e) {
                        logger.debug("Failed to parse flow line: {}", line);
                    }
                }
            }
            
            process.waitFor(10, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            logger.error("Error reading flows", e);
        }
        
        return flows;
    }
    
    /**
     * 特定のURLパターンにマッチするフローを取得
     * 
     * @param urlPattern URLパターン（正規表現）
     * @return マッチするフローのリスト
     */
    public List<JsonNode> getFlowsByUrlPattern(String urlPattern) {
        return getFlows().stream()
            .filter(flow -> {
                try {
                    JsonNode request = flow.get("request");
                    if (request != null) {
                        String url = request.get("url").asText();
                        return url.matches(urlPattern);
                    }
                } catch (Exception e) {
                    logger.debug("Error filtering flow", e);
                }
                return false;
            })
            .toList();
    }
    
    /**
     * フローデータをクリア
     */
    public void clearFlows() {
        try {
            if (Files.exists(flowFile)) {
                Files.delete(flowFile);
            }
            logger.debug("Flow data cleared");
        } catch (IOException e) {
            logger.error("Error clearing flows", e);
        }
    }
    
    /**
     * プロキシポート番号を取得
     * 
     * @return プロキシポート番号
     */
    public int getProxyPort() {
        return proxyPort;
    }
    
    /**
     * プロキシが実行中かどうかを確認
     * 
     * @return 実行中の場合true
     */
    public boolean isRunning() {
        return isRunning && mitmproxyProcess != null && mitmproxyProcess.isAlive();
    }
    
    /**
     * リソースを解放
     */
    public void cleanup() {
        stop();
        
        try {
            // 一時ファイルを削除
            if (tempDir != null && Files.exists(tempDir)) {
                Files.walk(tempDir)
                    .sorted((a, b) -> b.compareTo(a)) // ディレクトリを最後に削除
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            logger.debug("Failed to delete: {}", path);
                        }
                    });
            }
            
            logger.info("MitmproxyManager cleaned up");
        } catch (IOException e) {
            logger.error("Error during cleanup", e);
        }
    }
    
    /**
     * mitmproxyコマンドを構築
     */
    private List<String> buildMitmproxyCommand() {
        List<String> command = new ArrayList<>();
        command.add("mitmdump");
        command.add("--listen-port");
        command.add(String.valueOf(proxyPort));
        command.add("--set");
        command.add("confdir=" + tempDir.toString());
        command.add("-w");
        command.add(flowFile.toString());
        command.add("-s");
        command.add(addonFile.toString());
        
        return command;
    }
    
    /**
     * カスタムアドオンファイルを作成
     * zstdエンコーディングの除去とログ出力を行う
     */
    private void createCustomAddon() throws IOException {
        String addonContent = """
            \"\"\"
            Custom mitmproxy addon for Java integration
            - Remove zstd encoding from Accept-Encoding header
            - Log request/response information
            \"\"\"
            
            import logging
            from mitmproxy import http
            
            class JavaIntegrationAddon:
                def request(self, flow: http.HTTPFlow) -> None:
                    # Remove zstd from Accept-Encoding header
                    accept_encoding = flow.request.headers.get("Accept-Encoding", "")
                    if "zstd" in accept_encoding:
                        new_encoding = accept_encoding.replace(", zstd", "").replace("zstd, ", "").replace("zstd", "")
                        flow.request.headers["Accept-Encoding"] = new_encoding
                        logging.info(f"Removed zstd from Accept-Encoding: {flow.request.url}")
                
                def response(self, flow: http.HTTPFlow) -> None:
                    if flow.response:
                        logging.info(f"Response: {flow.response.status_code} {flow.request.url}")
            
            addons = [JavaIntegrationAddon()]
            """;
        
        Files.write(addonFile, addonContent.getBytes());
        logger.debug("Custom addon created: {}", addonFile);
    }
    
    /**
     * プロセスの出力を監視
     */
    private void startOutputMonitoring() {
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(mitmproxyProcess.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.debug("Mitmproxy output: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Output monitoring stopped");
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();
        
        Thread errorThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(mitmproxyProcess.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.warn("Mitmproxy error: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error monitoring stopped");
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
