package com.mrcresearch.service.tools;

import com.browserup.bup.BrowserUpProxy;
import com.browserup.bup.BrowserUpProxyServer;
import com.browserup.bup.client.ClientUtil;
import com.browserup.bup.proxy.CaptureType;
import com.browserup.harreader.model.HarEntry;
import com.browserup.harreader.model.HttpMethod;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mrcresearch.security.SecureDriverUtil;
import com.mrcresearch.service.analyze.model.GetItemModel;
import com.mrcresearch.service.common.ItemStatus;
import com.mrcresearch.service.common.JavaScripts;
import com.mrcresearch.service.common.MercariApi;
import com.mrcresearch.service.models.sellerItem.ItemData;
import com.mrcresearch.service.models.sellerItem.SellerItem;
import com.mrcresearch.util.database.SettingsRepository;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxBinary;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.Inet4Address;
import java.net.Socket;
import java.net.UnknownHostException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;


public class DriverTool {
    private static final Logger logger = LoggerFactory.getLogger(DriverTool.class);
    /**
     * ページのロード最大時間
     */
    private final InfoUtil info = new InfoUtil();
    public BrowserUpProxy proxy;
    public WebDriver driver;
    /**
     * ロードしてからの標準待ち時間
     */
    private int sleepTime;

    /**
     * ドライバ・プロキシの初期設定を行う
     */
    public void startDriver(boolean runHeadless) {
        sleepTime = SettingsRepository.getBrowserSleepTime() * 1000;

        // Macの場合はバイナリを設定する必要あり
        if (PlatFormUtils.isMac()) {
            System.setProperty("webdriver.firefox.bin", "/Applications/Firefox.app/Contents/MacOS/firefox");
        }
        // Windowsの場合、普通に取得できない場合はバイナリを設定する必要あり
        if (PlatFormUtils.isWindows()) {
            try {
                info.printDebug(String.valueOf(new FirefoxBinary()));
            } catch (Exception e) {
                System.setProperty("webdriver.firefox.driver", "C:\\Program Files\\Mozilla Firefox\\Firefox.exe");
            }
        }

        // Firefoxのパス、システムの状態を確認する(バグ起きたとき用にチェックする)
        try {
            info.printDebug(String.valueOf(new FirefoxBinary()));
        } catch (Exception e) {
            info.printDebug("failed to get default firefox binary");
        }
        try {
            info.printDebug("Platform : " + PlatFormUtils.getOS_NAME());
        } catch (Exception e) {
            info.printDebug("failed to get platform");
        }

        // BrowserUpProxyServer の設定
        proxy = new BrowserUpProxyServer();

        // zstdエンコーディングをリクエストヘッダから削除するフィルターを追加
        proxy.addRequestFilter((request, contents, messageInfo) -> {
            // Accept-Encoding ヘッダから "zstd" を削除する
            String acceptEncoding = request.headers().get("Accept-Encoding");
            if (acceptEncoding != null && acceptEncoding.contains("zstd")) {
                String newAcceptEncoding = acceptEncoding.replace(", zstd", "").replace("zstd, ", "").replace("zstd", "");
                request.headers().set("Accept-Encoding", newAcceptEncoding);
            }
            return null;
        });

        startProxy();

        // WebDriverを設定する
        try {
            setWebDriver(runHeadless, SettingsRepository.getBrowserType().toLowerCase());
        } catch (Exception e) {
            // エラーが発生した場合、エラーメッセージをデバッグ情報として出力する
            info.printDebug(e.getMessage());
        }

        int initSleepTIme = SettingsRepository.getBrowserInitSleepTime() * 1000;
        try {
            Thread.sleep(initSleepTIme);
        } catch (InterruptedException e) {
            info.printDebug(e.getMessage());
            Thread.currentThread().interrupt();
        }
        // ページが完全にロードされるまでの時間を設定
        driver.manage().timeouts().pageLoadTimeout(SettingsRepository.getBrowserLoadTimeout(), TimeUnit.SECONDS);
        driver.manage().window().maximize();

        info.printDebug("Driver Started!!");
    }

    /**
     * WebDriverの設定を行う
     *
     * @param runHeadless ヘッドレスモードで起動するか
     * @param browserType ブラウザー種別
     * @throws UnknownHostException ホストのアドレス
     */
    private void setWebDriver(boolean runHeadless, String browserType) throws UnknownHostException {
        // キャプチャする情報を選択する(レスポンスのみ取得する)
        EnumSet<CaptureType> captureTypes = CaptureType.getResponseCaptureTypes();
        proxy.enableHarCaptureTypes(captureTypes);

        // Seleniumのプロキシを設定する
        Proxy seleniumProxy = ClientUtil.createSeleniumProxy(proxy);
        String hostIp = Inet4Address.getLocalHost().getHostAddress();
        seleniumProxy.setHttpProxy(hostIp + ":" + proxy.getPort());
        seleniumProxy.setSslProxy(hostIp + ":" + proxy.getPort());

        // ブラウザを起動する（Selenium Manager によるドライバ解決を使用）
        switch (browserType) {
            case "chrome":
//                ChromeOptions options = SecureDriverUtil.getSecureChromeOptions();
                ChromeOptions options = new ChromeOptions();
                if (runHeadless) options.addArguments("--headless");
                options.setProxy(seleniumProxy);
                options.setAcceptInsecureCerts(true);
                driver = new ChromeDriver(options);
                break;
            case "firefox":
//                FirefoxOptions firefoxOptions = SecureDriverUtil.getSecureFirefoxOptions();
                FirefoxOptions firefoxOptions = new FirefoxOptions();
                if (runHeadless) firefoxOptions.addArguments("--headless");
                firefoxOptions.setProxy(seleniumProxy);
                firefoxOptions.setAcceptInsecureCerts(true);
                driver = new FirefoxDriver(firefoxOptions);
                break;
            default:
                // デフォルトはChrome
                ChromeOptions defaultOptions = SecureDriverUtil.getSecureChromeOptions();
                if (runHeadless) defaultOptions.addArguments("--headless");
                defaultOptions.setProxy(seleniumProxy);
                defaultOptions.setAcceptInsecureCerts(true);
                driver = new ChromeDriver(defaultOptions);
                break;
        }
    }

    /**
     * ドライバ・プロキシを終了する
     */
    public void stopDriver() {
        info.printDebug("Driver started closing");

        // WebDriverの終了処理
        try {
            if (driver != null) {
                driver.quit();
                driver = null;
            }
        } catch (Exception e) {
            // WebDriverの終了に関するエラーは無視
            // ブラウザ自体は終了しているケースが多いため
            info.printDebug("WebDriver quit exception ignored: " + e.getMessage());
        }

        // プロキシの終了処理
        try {
            if (proxy != null) {
                proxy.stop();
                proxy = null;
            }
        } catch (Exception e) {
            // プロキシの終了に関するエラーは無視
            info.printDebug("Proxy stop exception ignored: " + e.getMessage());
        }

        info.printDebug("Driver closed.");
    }

    /**
     * プロフィール画面のHARを取得し、Stringで返す
     *
     * @param sellerId セラー情報
     */
    public void getProfileHar(String sellerId) {
        proxy.newHar();
        String url = "https://jp.mercari.com/user/profile/" + sellerId;
        openAndWait(url);

        // 「もっと見る」を限界までクリックする
        boolean running = true;
        int cnt = 0;
        while (running) {
            try {
                TimeUnit.MILLISECONDS.sleep(sleepTime / 2);
                WebElement moreButton = driver.findElements(By.className("merButton")).stream()
                        .filter(n -> n.getText().equals("もっと見る")).findFirst().orElse(null);
                if (Objects.nonNull(moreButton)) {
                    moreButton.click();
                    cnt++;
                    logger.info("「もっと見る」をクリックしました(" + cnt + ")回目");
                    TimeUnit.MILLISECONDS.sleep(sleepTime);
                } else { // 「もっと見る」ボタンが表示されなければ終了する
                    break;
                }
                // 古い商品数のカウントを取得する
                running = countOldItems();
            } catch (Exception e) {
                info.printDebug(e.getMessage());
            }
        }
        logger.info("{} 回クリックしました", cnt);
    }

    private boolean countOldItems() throws JsonProcessingException {
        List<ItemData> items = getsellerItemData();
        int compareDate = SettingsRepository.getSoldDate();

        // 売り切れで指定日数以前のアイテムをカウント
        long oldItemsCount = items.stream()
                .filter(item -> !ItemStatus.isOnSale(item.getStatus()) &&
                        isOldDate(item.getUpdated(), compareDate))
                .count();

        logger.info("古いアイテム数: {}", oldItemsCount);

        // 設定された表示数より多いかどうかを返す
        return oldItemsCount < SettingsRepository.getOldItemDisplayCount();
    }

    /**
     * タイムスタンプが指定した日数以前のものかどうかを判定する
     *
     * @param timeStamp   Unix タイムスタンプ（秒単位）
     * @param compareDate 比較する日数（何日前か）
     * @return 指定した日数以前の場合はtrue、そうでない場合はfalse
     */
    private boolean isOldDate(String timeStamp, int compareDate) {
        if (timeStamp == null || timeStamp.isEmpty()) {
            return false;
        }

        try {
            // タイムスタンプを数値に変換
            long timestamp = Long.parseLong(timeStamp);

            // タイムスタンプをLocalDateTimeに変換
            Instant instant = Instant.ofEpochSecond(timestamp);
            LocalDateTime itemDate = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Tokyo"));

            // 現在の日時から指定した日数前の日時を計算
            LocalDateTime compareDateBefore = LocalDateTime.now().minusDays(compareDate);

            // 指定した日数以前かどうかを判定
            return itemDate.isBefore(compareDateBefore);
        } catch (NumberFormatException e) {
            info.printDebug("Error parsing timestamp: " + timeStamp + " - " + e.getMessage());
            return false;
        } catch (Exception e) {
            info.printDebug("Error comparing dates: " + e.getMessage());
            return false;
        }
    }

    /**
     * ページを開いて何秒か待つ
     *
     * @param url 開くURL
     */
    public void openAndWait(String url) {
        try {
            driver.get(url);
            TimeUnit.MILLISECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            info.printDebug(e.getLocalizedMessage());
            Thread.currentThread().interrupt();
        }
    }

    private void startProxy() {
        int portNum = 8888;
        while (!isPortAvailable(portNum)) {
            portNum++;
        }
        proxy.start(portNum);
// TODO これを使って今後アクセスと同時にDB操作できるようになるともっとよい？？
//        proxy.addFirstHttpFilterFactory(new HttpFiltersSourceAdapter() {
//            @Override
//            public HttpFilters filterRequest(HttpRequest originalRequest, ChannelHandlerContext ctx) {
//                return new HttpFiltersAdapter(originalRequest) {
//                    @Override
//                    public HttpObject serverToProxyResponse(HttpObject httpObject) {
//                        // ここでレスポンスに対する処理を記述します。
//                        // 例えば、特定のURLのレスポンス内容を取得するなど。
//                        if (originalRequest.getUri().contains("mercari")) {
//                            if (httpObject instanceof HttpResponse) {
//                                logger.info("Response URL: " + originalRequest.getUri());
//                                logger.info("Response Status: " + ((HttpResponse) httpObject).getStatus());
//                            }
//                            if (httpObject instanceof HttpContent) {
//                                HttpContent content = (HttpContent) httpObject;
//                                logger.info("Response Content: " + content.content().toString(java.nio.charset.StandardCharsets.UTF_8));
//                                // logger.info(content.content().toString(java.nio.charset.StandardCharsets.UTF_8));
//                            }
//                        }
//                        return httpObject;
//                    }
//                };
//            }
//        });
    }

    /**
     * ポートが空いているか確認する。<br>
     * ポートが空いているときはエラーになる。<br>
     *
     * @param port 確認するポート番号
     * @return ポートが開いているか
     */
    private boolean isPortAvailable(int port) {
        info.printDebug("Testing port " + port);

        //noinspection unused ポートが開いているか確認するだけなので気にしない
        try (Socket sk = new Socket("localhost", port)) {
            info.printDebug("Port " + port + " is not available");
            return false;
        } catch (IOException e) {
            info.printDebug("Port " + port + " is available");
            return true;
        }
    }

    /**
     * 取得したHARからアイテムリストを返す（売れたアイテムのみ）
     *
     * @return アイテムリスト
     * @throws IOException 取得に失敗したときに例外をスローする
     */
    public List<GetItemModel> getItemListFromHAR() throws IOException {
        info.printDebug("Analyzing items from HAR");
        List<GetItemModel> itemList = new ArrayList<>();

        HashMap<String, Integer> itemMap = new HashMap<>();

        List<ItemData> items = getsellerItemData();

        // 1つ1つアイテムを入れていく
        for (ItemData item : items) {
            //販売中（on_sale)は除くとtradingとsold_outになる（売れたものはこれを見ればよい
            if (!itemMap.containsKey(item.getId())) {
                GetItemModel addItem = new GetItemModel(item);

                // 画像を取得してファイルに保存
                String imageUrl = item.getThumbnails().get(0);
                byte[] image = ImageTool.getImageFromList(getHarEntries(), imageUrl);

                // HARから取得できない場合は、サムネイルURLから直接ダウンロード
                if (image == null && imageUrl != null) {
                    image = ImageTool.downloadImage(imageUrl);
                }

                if (image != null) {
                    String savedPath = ImageTool.saveImageToUserFolder(image, item.getId());
                    if (savedPath != null) {
                        // 保存に成功した場合、thumbnailUrlを保存パスに更新
                        addItem.setThumbnailUrl(savedPath);
                    }
                }
                addItem.setImage(image);
                itemList.add(addItem);
                // マップに登録
                itemMap.put(item.getId(), 1);
            }
        }


        return itemList;
    }

    private List<ItemData> getsellerItemData() throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();

        List<ItemData> items = new ArrayList<>();
        List<HarEntry> entries = getSellerItemEntries();

        for (HarEntry entry : entries) {
            // そのままでは解析できないため、いったんStringに変えてあげる
            String nodeTxt = entry.getResponse().getContent().getText();
            if (Objects.isNull(nodeTxt)) {
                continue;
            }
            items.addAll(mapper.readValue(nodeTxt, SellerItem.class).getItemData());
        }
        return items;
    }

    private List<HarEntry> getSellerItemEntries() {
        return getHarEntries().stream().filter(
                entry -> entry.getRequest().getUrl().contains(MercariApi.SELLER_ITEMS)
                        && HttpMethod.GET.equals(entry.getRequest().getMethod())).toList();
    }

    /**
     * 画面の一番下までスクロールする<br />
     * 画像情報読み込み用<br />
     */
    public void scrollToBottom() {
        Long beforeHeight = (Long) executeJavaScriptWithReturn("document.body.scrollHeight;");
        executeJavaScript("scroll(0, document.body.scrollHeight);");
        // いったん待ってから高さをもう一度取得する
        try {
            Thread.sleep(sleepTime);
        } catch (InterruptedException e) {
            info.printDebug(e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
        Long afterHeight = (Long) executeJavaScriptWithReturn("document.body.scrollHeight;");

        // 高さが変わった場合はもう一度実行する
        if (!Objects.equals(beforeHeight, afterHeight)) {
            JavaScripts.stopLazyLoad(this);
            JavaScripts.ScrollToBottom(this);
        }
    }

    /**
     * JavaScriptを実行する
     *
     * @param script 実行スクリプト
     */
    public void executeJavaScript(String script) {
        ((JavascriptExecutor) driver).executeScript(script);
    }

    /**
     * JavaScriptを実行し、取得した値を返す
     *
     * @param script 実行スクリプト
     * @return オブジェクト
     */
    public Object executeJavaScriptWithReturn(String script) {
        return ((JavascriptExecutor) driver).executeScript("return " + script);
    }

    /**
     * Harエントリーのリストを返却する
     *
     * @return HARエントリー
     */
    public List<HarEntry> getHarEntries() {
        return proxy.getHar().getLog().getEntries();
    }

    /**
     * 指定した要素をクリックする
     *
     * @param element クリックする要素
     */
    public void clickElement(WebElement element) {
        executeJavaScript("scroll(0, " + (element.getLocation().y + 10) + ");");
        // クリックができないとき、aタグのhrefを取得して直接ページを開く
        try {
            Objects.requireNonNull(element).click();
        } catch (Exception e) {
            try {
                // element内のaタグの情報を取得する
                String nextpage = element.findElement(By.tagName("a")).getAttribute("href");
                driver.get(nextpage);
            } catch (Exception ignored) {
            }
        }
    }

}