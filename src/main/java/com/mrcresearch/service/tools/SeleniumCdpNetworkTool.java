package com.mrcresearch.service.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.HasDevTools;
import org.openqa.selenium.devtools.NetworkInterceptor;
import org.openqa.selenium.devtools.v137.network.Network;
import org.openqa.selenium.devtools.v137.network.model.RequestId;
import org.openqa.selenium.devtools.v137.network.model.Response;
import org.openqa.selenium.remote.http.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Selenium 4 CDP (Chrome DevTools Protocol) を使用したネットワーク監視ツール
 * BrowserUp Proxyの代替として、Seleniumの組み込み機能を使用してネットワークトラフィックを監視・記録する
 */
public class SeleniumCdpNetworkTool {
    private static final Logger logger = LoggerFactory.getLogger(SeleniumCdpNetworkTool.class);
    
    private WebDriver driver;
    private DevTools devTools;
    private NetworkInterceptor networkInterceptor;
    private final List<NetworkEntry> networkEntries = new CopyOnWriteArrayList<>();
    private final Map<String, NetworkEntry> pendingRequests = new ConcurrentHashMap<>();
    private boolean isMonitoring = false;
    
    /**
     * ネットワークエントリを表すクラス（HAR形式に類似）
     */
    public static class NetworkEntry {
        private String requestId;
        private String url;
        private String method;
        private Map<String, String> requestHeaders = new HashMap<>();
        private Map<String, String> responseHeaders = new HashMap<>();
        private String requestBody;
        private String responseBody;
        private int statusCode;
        private String statusText;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private long duration;
        
        // Getters and Setters
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public Map<String, String> getRequestHeaders() { return requestHeaders; }
        public void setRequestHeaders(Map<String, String> requestHeaders) { this.requestHeaders = requestHeaders; }
        
        public Map<String, String> getResponseHeaders() { return responseHeaders; }
        public void setResponseHeaders(Map<String, String> responseHeaders) { this.responseHeaders = responseHeaders; }
        
        public String getRequestBody() { return requestBody; }
        public void setRequestBody(String requestBody) { this.requestBody = requestBody; }
        
        public String getResponseBody() { return responseBody; }
        public void setResponseBody(String responseBody) { this.responseBody = responseBody; }
        
        public int getStatusCode() { return statusCode; }
        public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
        
        public String getStatusText() { return statusText; }
        public void setStatusText(String statusText) { this.statusText = statusText; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
    }
    
    /**
     * WebDriverを設定してネットワーク監視を初期化
     * 
     * @param driver ChromeDriverインスタンス
     */
    public void initialize(WebDriver driver) {
        if (!(driver instanceof ChromeDriver)) {
            throw new IllegalArgumentException("CDP network monitoring requires ChromeDriver");
        }
        
        this.driver = driver;
        this.devTools = ((HasDevTools) driver).getDevTools();
        this.devTools.createSession();
        
        logger.info("Selenium CDP Network Tool initialized");
    }
    
    /**
     * ネットワーク監視を開始
     * zstdエンコーディングの除去とレスポンス記録を行う
     */
    public void startMonitoring() {
        if (isMonitoring) {
            logger.warn("Network monitoring is already active");
            return;
        }
        
        try {
            // ネットワークドメインを有効化
            devTools.send(Network.enable(Optional.empty(), Optional.empty(), Optional.empty()));
            
            // リクエスト/レスポンスインターセプターを設定
            networkInterceptor = new NetworkInterceptor(driver, createNetworkFilter());
            
            // ネットワークイベントリスナーを設定
            setupNetworkEventListeners();
            
            isMonitoring = true;
            logger.info("Network monitoring started");
            
        } catch (Exception e) {
            logger.error("Failed to start network monitoring", e);
            throw new RuntimeException("Failed to start network monitoring", e);
        }
    }
    
    /**
     * ネットワーク監視を停止
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        try {
            if (networkInterceptor != null) {
                networkInterceptor.close();
                networkInterceptor = null;
            }
            
            // ネットワークドメインを無効化
            devTools.send(Network.disable());
            
            isMonitoring = false;
            logger.info("Network monitoring stopped");
            
        } catch (Exception e) {
            logger.error("Error stopping network monitoring", e);
        }
    }
    
    /**
     * ネットワークフィルターを作成
     * zstdエンコーディングの除去を行う
     */
    private Filter createNetworkFilter() {
        return next -> req -> {
            try {
                // Accept-Encoding ヘッダから "zstd" を削除
                String acceptEncoding = req.getHeader("Accept-Encoding");
                if (acceptEncoding != null && acceptEncoding.contains("zstd")) {
                    String newAcceptEncoding = acceptEncoding
                        .replace(", zstd", "")
                        .replace("zstd, ", "")
                        .replace("zstd", "");
                    req = req.addHeader("Accept-Encoding", newAcceptEncoding);
                }
                
                // リクエストを実行
                HttpResponse response = next.execute(req);
                
                return response;
                
            } catch (Exception e) {
                logger.warn("Error in network filter: " + e.getMessage());
                return next.execute(req);
            }
        };
    }
    
    /**
     * ネットワークイベントリスナーを設定
     */
    private void setupNetworkEventListeners() {
        // リクエスト開始イベント
        devTools.addListener(Network.requestWillBeSent(), event -> {
            try {
                NetworkEntry entry = new NetworkEntry();
                entry.setRequestId(event.getRequestId().toString());
                entry.setUrl(event.getRequest().getUrl());
                entry.setMethod(event.getRequest().getMethod());
                entry.setStartTime(LocalDateTime.now());
                
                // リクエストヘッダーを記録
                if (event.getRequest().getHeaders().isPresent()) {
                    Map<String, String> headers = new HashMap<>();
                    event.getRequest().getHeaders().get().toJson().fields()
                        .forEachRemaining(field -> headers.put(field.getKey(), field.getValue().asText()));
                    entry.setRequestHeaders(headers);
                }
                
                pendingRequests.put(entry.getRequestId(), entry);
                
            } catch (Exception e) {
                logger.warn("Error processing request event: " + e.getMessage());
            }
        });
        
        // レスポンス受信イベント
        devTools.addListener(Network.responseReceived(), event -> {
            try {
                String requestId = event.getRequestId().toString();
                NetworkEntry entry = pendingRequests.get(requestId);
                
                if (entry != null) {
                    Response response = event.getResponse();
                    entry.setStatusCode(response.getStatus());
                    entry.setStatusText(response.getStatusText());
                    entry.setEndTime(LocalDateTime.now());
                    
                    // レスポンスヘッダーを記録
                    if (response.getHeaders().isPresent()) {
                        Map<String, String> headers = new HashMap<>();
                        response.getHeaders().get().toJson().fields()
                            .forEachRemaining(field -> headers.put(field.getKey(), field.getValue().asText()));
                        entry.setResponseHeaders(headers);
                    }
                    
                    // 継続時間を計算
                    if (entry.getStartTime() != null && entry.getEndTime() != null) {
                        entry.setDuration(java.time.Duration.between(entry.getStartTime(), entry.getEndTime()).toMillis());
                    }
                }
                
            } catch (Exception e) {
                logger.warn("Error processing response event: " + e.getMessage());
            }
        });
        
        // ロード完了イベント
        devTools.addListener(Network.loadingFinished(), event -> {
            try {
                String requestId = event.getRequestId().toString();
                NetworkEntry entry = pendingRequests.remove(requestId);
                
                if (entry != null) {
                    networkEntries.add(entry);
                    logger.debug("Network entry completed: {} {}", entry.getMethod(), entry.getUrl());
                }
                
            } catch (Exception e) {
                logger.warn("Error processing loading finished event: " + e.getMessage());
            }
        });
    }
    
    /**
     * 記録されたネットワークエントリを取得
     * 
     * @return ネットワークエントリのリスト
     */
    public List<NetworkEntry> getNetworkEntries() {
        return new ArrayList<>(networkEntries);
    }
    
    /**
     * 特定のURLパターンにマッチするエントリを取得
     * 
     * @param urlPattern URLパターン（正規表現）
     * @return マッチするネットワークエントリのリスト
     */
    public List<NetworkEntry> getEntriesByUrlPattern(String urlPattern) {
        return networkEntries.stream()
            .filter(entry -> entry.getUrl().matches(urlPattern))
            .toList();
    }
    
    /**
     * ネットワークエントリをクリア
     */
    public void clearEntries() {
        networkEntries.clear();
        pendingRequests.clear();
        logger.debug("Network entries cleared");
    }
    
    /**
     * リソースを解放
     */
    public void cleanup() {
        stopMonitoring();
        
        if (devTools != null) {
            try {
                devTools.close();
            } catch (Exception e) {
                logger.warn("Error closing DevTools session: " + e.getMessage());
            }
        }
        
        clearEntries();
        logger.info("Selenium CDP Network Tool cleaned up");
    }
}
