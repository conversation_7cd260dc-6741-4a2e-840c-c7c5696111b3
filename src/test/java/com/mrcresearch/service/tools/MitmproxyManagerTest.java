package com.mrcresearch.service.tools;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.Inet4Address;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MitmproxyManagerのテストクラス
 * 
 * 注意: このテストを実行するには、システムにmitmproxyがインストールされている必要があります。
 * インストール方法:
 * - Windows: pip install mitmproxy または Microsoft Store
 * - macOS: brew install mitmproxy
 * - Linux: pip install mitmproxy
 */
@EnabledIf("isMitmproxyAvailable")
public class MitmproxyManagerTest {
    private static final Logger logger = LoggerFactory.getLogger(MitmproxyManagerTest.class);
    
    private MitmproxyManager mitmproxyManager;
    private WebDriver driver;
    
    /**
     * mitmproxyが利用可能かどうかをチェック
     */
    static boolean isMitmproxyAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("mitmdump", "--version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            System.out.println("mitmproxy is not available, skipping tests: " + e.getMessage());
            return false;
        }
    }
    
    @BeforeEach
    void setUp() {
        // テスト用のポートでmitmproxyを初期化
        mitmproxyManager = new MitmproxyManager(8889);
    }
    
    @AfterEach
    void tearDown() {
        if (driver != null) {
            try {
                driver.quit();
            } catch (Exception e) {
                logger.debug("Driver quit error: " + e.getMessage());
            }
        }
        
        if (mitmproxyManager != null) {
            try {
                mitmproxyManager.cleanup();
            } catch (Exception e) {
                logger.debug("Mitmproxy cleanup error: " + e.getMessage());
            }
        }
    }
    
    @Test
    void testMitmproxyStartStop() {
        // mitmproxyを開始
        assertDoesNotThrow(() -> mitmproxyManager.start());
        
        // 少し待機
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 実行状態を確認
        assertTrue(mitmproxyManager.isRunning(), "Mitmproxy should be running");
        assertEquals(8889, mitmproxyManager.getProxyPort(), "Proxy port should be 8889");
        
        // mitmproxyを停止
        assertDoesNotThrow(() -> mitmproxyManager.stop());
        
        // 停止状態を確認
        assertFalse(mitmproxyManager.isRunning(), "Mitmproxy should be stopped");
    }
    
    @Test
    void testWebDriverIntegration() {
        // mitmproxyを開始
        mitmproxyManager.start();
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // WebDriverでプロキシを使用
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--ignore-certificate-errors");
        options.addArguments("--ignore-ssl-errors");
        options.addArguments("--allow-running-insecure-content");
        
        // プロキシ設定
        try {
            String hostIp = Inet4Address.getLocalHost().getHostAddress();
            String proxyAddress = hostIp + ":" + mitmproxyManager.getProxyPort();
            options.addArguments("--proxy-server=http://" + proxyAddress);
            
            driver = new ChromeDriver(options);
            
            // テストページにアクセス
            driver.get("http://httpbin.org/get");
            
            // 少し待機してリクエストが記録されるのを待つ
            Thread.sleep(2000);
            
            // フローデータを取得
            List<JsonNode> flows = mitmproxyManager.getFlows();
            
            // フローが記録されていることを確認
            assertFalse(flows.isEmpty(), "At least one flow should be recorded");
            
            // httpbin.orgへのリクエストが含まれていることを確認
            boolean foundHttpbinRequest = flows.stream()
                .anyMatch(flow -> {
                    try {
                        JsonNode request = flow.get("request");
                        if (request != null) {
                            String url = request.get("url").asText();
                            return url.contains("httpbin.org");
                        }
                    } catch (Exception e) {
                        logger.debug("Error checking flow: " + e.getMessage());
                    }
                    return false;
                });
            
            assertTrue(foundHttpbinRequest, "Should find httpbin.org request in flows");
            
        } catch (Exception e) {
            fail("WebDriver integration test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testFlowFiltering() {
        // mitmproxyを開始
        mitmproxyManager.start();
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // WebDriverでプロキシを使用
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--ignore-certificate-errors");
        
        try {
            String hostIp = Inet4Address.getLocalHost().getHostAddress();
            String proxyAddress = hostIp + ":" + mitmproxyManager.getProxyPort();
            options.addArguments("--proxy-server=http://" + proxyAddress);
            
            driver = new ChromeDriver(options);
            
            // 複数のページにアクセス
            driver.get("http://httpbin.org/get");
            Thread.sleep(1000);
            driver.get("http://httpbin.org/json");
            Thread.sleep(1000);
            
            // フローデータを取得
            List<JsonNode> allFlows = mitmproxyManager.getFlows();
            List<JsonNode> jsonFlows = mitmproxyManager.getFlowsByUrlPattern(".*httpbin\\.org/json.*");
            
            // フィルタリングが正しく動作することを確認
            assertFalse(allFlows.isEmpty(), "Should have some flows");
            assertFalse(jsonFlows.isEmpty(), "Should have JSON endpoint flows");
            assertTrue(jsonFlows.size() <= allFlows.size(), "Filtered flows should be subset of all flows");
            
        } catch (Exception e) {
            fail("Flow filtering test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testFlowClear() {
        // mitmproxyを開始
        mitmproxyManager.start();
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // WebDriverでプロキシを使用
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        
        try {
            String hostIp = Inet4Address.getLocalHost().getHostAddress();
            String proxyAddress = hostIp + ":" + mitmproxyManager.getProxyPort();
            options.addArguments("--proxy-server=http://" + proxyAddress);
            
            driver = new ChromeDriver(options);
            
            // テストページにアクセス
            driver.get("http://httpbin.org/get");
            Thread.sleep(2000);
            
            // フローが記録されていることを確認
            List<JsonNode> flowsBeforeClear = mitmproxyManager.getFlows();
            assertFalse(flowsBeforeClear.isEmpty(), "Should have flows before clear");
            
            // フローをクリア
            mitmproxyManager.clearFlows();
            
            // フローがクリアされていることを確認
            List<JsonNode> flowsAfterClear = mitmproxyManager.getFlows();
            assertTrue(flowsAfterClear.isEmpty(), "Should have no flows after clear");
            
        } catch (Exception e) {
            fail("Flow clear test failed: " + e.getMessage());
        }
    }
}
