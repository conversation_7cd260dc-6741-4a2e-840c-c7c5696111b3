# mitmproxy セットアップガイド

## 概要

このガイドでは、BrowserUp Proxyの代替として実装されたmitmproxy統合機能のセットアップ方法を説明します。

## 前提条件

### システム要件
- Java 11以上
- Python 3.8以上（mitmproxyのため）
- Chrome または Firefox ブラウザ

### 対応OS
- Windows 10/11
- macOS 10.15以上
- Ubuntu 18.04以上

## mitmproxyのインストール

### Windows

#### 方法1: Microsoft Store（推奨）
1. Microsoft Storeを開く
2. "mitmproxy"を検索
3. インストールボタンをクリック

#### 方法2: pip
```bash
# Python 3.8以上がインストールされていることを確認
python --version

# pipでインストール
pip install mitmproxy

# インストール確認
mitmdump --version
```

#### 方法3: バイナリダウンロード
1. [mitmproxy公式サイト](https://mitmproxy.org/)からWindows用バイナリをダウンロード
2. ダウンロードしたインストーラーを実行
3. インストール完了後、コマンドプロンプトで `mitmdump --version` を実行して確認

### macOS

#### 方法1: Homebrew（推奨）
```bash
# Homebrewでインストール
brew install mitmproxy

# インストール確認
mitmdump --version
```

#### 方法2: pip
```bash
# Python 3.8以上がインストールされていることを確認
python3 --version

# pipでインストール
pip3 install mitmproxy

# インストール確認
mitmdump --version
```

### Linux (Ubuntu/Debian)

#### 方法1: pip（推奨）
```bash
# 必要なパッケージをインストール
sudo apt update
sudo apt install python3 python3-pip

# mitmproxyをインストール
pip3 install mitmproxy

# パスを通す（必要に応じて）
echo 'export PATH=$PATH:~/.local/bin' >> ~/.bashrc
source ~/.bashrc

# インストール確認
mitmdump --version
```

#### 方法2: バイナリダウンロード
```bash
# 最新版をダウンロード
wget https://snapshots.mitmproxy.org/12.0/mitmproxy-12.0-linux-x86_64.tar.gz

# 展開
tar -xzf mitmproxy-12.0-linux-x86_64.tar.gz

# パスに追加
sudo mv mitmproxy mitmdump mitmweb /usr/local/bin/

# インストール確認
mitmdump --version
```

## アプリケーションでの使用方法

### 基本的な使用方法

```java
import com.mrcresearch.service.tools.DriverTool;

public class Example {
    public void useMitmproxy() {
        DriverTool driverTool = new DriverTool();
        
        try {
            // mitmproxyを使用してドライバを開始
            driverTool.startDriver(true, true); // ヘッドレス=true, mitmproxy=true
            
            // Webページにアクセス
            driverTool.driver.get("https://example.com");
            
            // フローデータを取得
            List<JsonNode> flows = driverTool.getFlows();
            
            // 特定のURLパターンでフィルタリング
            List<JsonNode> apiFlows = driverTool.getFlowsByUrlPattern(".*api.*");
            
            // フローデータを処理
            for (JsonNode flow : flows) {
                JsonNode request = flow.get("request");
                if (request != null) {
                    String url = request.get("url").asText();
                    String method = request.get("method").asText();
                    System.out.println(method + " " + url);
                }
            }
            
        } finally {
            // リソースを解放
            driverTool.stopDriver();
        }
    }
}
```

### 従来のBrowserUpProxyとの併用

```java
public class MigrationExample {
    public void gradualMigration() {
        DriverTool driverTool = new DriverTool();
        
        // 環境変数で切り替え
        boolean useMitmproxy = "true".equals(System.getenv("USE_MITMPROXY"));
        
        try {
            driverTool.startDriver(true, useMitmproxy);
            
            // 共通のWebDriver操作
            driverTool.driver.get("https://example.com");
            
            if (useMitmproxy) {
                // mitmproxyの場合
                List<JsonNode> flows = driverTool.getFlows();
                processFlows(flows);
            } else {
                // BrowserUpProxyの場合
                List<HarEntry> entries = driverTool.getHarEntries();
                processHarEntries(entries);
            }
            
        } finally {
            driverTool.stopDriver();
        }
    }
}
```

## トラブルシューティング

### よくある問題と解決方法

#### 1. "mitmdump: command not found"
**原因**: mitmproxyがインストールされていないか、PATHが通っていない

**解決方法**:
```bash
# インストール状況を確認
which mitmdump

# PATHを確認
echo $PATH

# 必要に応じてPATHを追加
export PATH=$PATH:~/.local/bin
```

#### 2. "Permission denied" エラー
**原因**: ポート8888が他のプロセスで使用されている

**解決方法**:
```java
// 別のポートを使用
MitmproxyManager manager = new MitmproxyManager(8889);
```

#### 3. SSL証明書エラー
**原因**: mitmproxyのSSL証明書が信頼されていない

**解決方法**:
```java
// ChromeOptionsでSSLエラーを無視
ChromeOptions options = new ChromeOptions();
options.addArguments("--ignore-certificate-errors");
options.addArguments("--ignore-ssl-errors");
options.addArguments("--allow-running-insecure-content");
```

#### 4. プロセスが終了しない
**原因**: mitmproxyプロセスが正常に終了していない

**解決方法**:
```bash
# プロセスを強制終了
pkill -f mitmdump

# Windowsの場合
taskkill /f /im mitmdump.exe
```

### ログの確認

#### アプリケーションログ
```java
// ログレベルを調整
System.setProperty("org.slf4j.simpleLogger.log.com.mrcresearch.service.tools.MitmproxyManager", "DEBUG");
```

#### mitmproxyログ
mitmproxyのログは一時ディレクトリに出力されます：
- Windows: `%TEMP%\mitmproxy_*\`
- macOS/Linux: `/tmp/mitmproxy_*/`

## パフォーマンス最適化

### メモリ使用量の削減
```java
// 定期的にフローデータをクリア
mitmproxyManager.clearFlows();

// 不要なフローをフィルタリング
List<JsonNode> relevantFlows = mitmproxyManager.getFlowsByUrlPattern(".*api\\.mercari\\..*");
```

### 起動時間の短縮
```java
// プロキシポートを固定
MitmproxyManager manager = new MitmproxyManager(8888);

// 起動待機時間を調整
Thread.sleep(1000); // デフォルトは2000ms
```

## セキュリティ考慮事項

### 証明書管理
- mitmproxyは自己署名証明書を使用します
- 本番環境では適切な証明書管理を行ってください
- テスト環境でのみSSLエラーを無視してください

### ネットワーク分離
- プロキシは localhost でのみ動作します
- 外部からのアクセスは制限されています
- ファイアウォール設定を確認してください

## 次のステップ

1. **開発環境での動作確認**
   - mitmproxyのインストール
   - テストケースの実行
   - 既存機能との比較

2. **段階的移行**
   - 環境変数による切り替え実装
   - 機能別の移行テスト
   - パフォーマンス測定

3. **本番展開**
   - 監視設定の追加
   - ロールバック計画の準備
   - ユーザー向けドキュメントの更新

## サポート

問題が発生した場合は、以下の情報を含めて報告してください：

- OS とバージョン
- Java バージョン
- mitmproxy バージョン
- エラーメッセージ
- 再現手順

詳細なログとスタックトレースも含めてください。
