# プロキシライブラリ移行計画

## 現在の状況

### 問題の原因
NettyのByteBufリソースリークエラーの主な原因は、**古いBrowserUp Proxyバージョン（2.1.2）**の使用でした。

### 発見された重要な事実
1. **BrowserUp Proxy は公式に非推奨（deprecated）**
2. **開発チームはmitmproxyへの移行を推奨**
3. **現在のアーキテクチャではWebSockets/HTTP/2サポートが困難**

## 即座の対応（完了済み）

### 1. バージョン更新
- **BrowserUp Proxy**: 2.1.2 → 3.0.0
- **Netty**: 4.1.108.Final → 4.1.115.Final

### 2. リソース管理の改善
- NettyResourceManagerクラスの追加
- DriverToolでのリソースクリーンアップ強化
- アプリケーション終了時の適切なシャットダウン処理

### 3. エラーハンドリングの強化
- HARデータ処理の安全性向上
- プロキシフィルターでのエラー処理追加
- ガベージコレクションの促進

## 中期的な移行計画（推奨）

### Phase 1: 安定性の確保（1-2週間）
- [ ] 更新されたバージョンでの動作確認
- [ ] メモリリークの監視
- [ ] パフォーマンステスト実行
- [ ] エラーログの分析

### Phase 2: 代替案の調査（2-4週間）
以下の選択肢を評価：

#### Option A: Selenium 4 CDP（最推奨）
**メリット:**
- Seleniumに組み込まれているため追加依存関係不要
- Chrome DevTools Protocolによる強力なネットワーク制御
- リクエスト/レスポンスインターセプト機能
- アクティブに開発・メンテナンスされている
- 既存のSelenium統合を活用可能

**デメリット:**
- ChromeDriverのみサポート（FirefoxはWebDriver BiDi待ち）
- HARファイル生成は自前実装が必要
- CDPのバージョン依存性

**実装例:**
```java
// ネットワークインターセプター
try (NetworkInterceptor ignored = new NetworkInterceptor(driver,
    (Filter) next -> req -> {
        // リクエストヘッダー変更
        String acceptEncoding = req.getHeader("Accept-Encoding");
        if (acceptEncoding != null && acceptEncoding.contains("zstd")) {
            req.setHeader("Accept-Encoding", acceptEncoding.replace("zstd", ""));
        }
        HttpResponse res = next.execute(req);
        // レスポンス記録
        recordResponse(req, res);
        return res;
    })) {
    driver.get(url);
}
```

#### Option B: LittleProxy + LittleProxy-mitm（推奨）
**メリット:**
- 軽量で高性能
- HTTPS MITMサポート
- 豊富なフィルタリング機能
- HARファイル生成の自前実装が可能
- BrowserUp Proxyと類似のAPI

**デメリット:**
- LittleProxy-mitmは開発停止（最終版1.1.0）
- SSL証明書管理が必要
- 既存コードの一部変更が必要

**実装例:**
```java
HttpProxyServer server = DefaultHttpProxyServer.bootstrap()
    .withPort(8888)
    .withManInTheMiddle(new CertificateSniffingMitmManager())
    .withFiltersSource(new HttpFiltersSourceAdapter() {
        @Override
        public HttpFilters filterRequest(HttpRequest originalRequest, ChannelHandlerContext ctx) {
            return new HttpFiltersAdapter(originalRequest) {
                @Override
                public HttpResponse clientToProxyRequest(HttpObject httpObject) {
                    // zstd除去処理
                    return null;
                }
            };
        }
    })
    .start();
```

#### Option C: mitmproxy（長期的選択肢）
**メリット:**
- BrowserUp開発チームが推奨
- HTTP/2, WebSocket, HTTP/3サポート
- アクティブな開発とメンテナンス
- Pythonベースで拡張性が高い

**デメリット:**
- Java統合に追加の作業が必要
- 学習コストが発生
- 既存コードの大幅な変更が必要
- プロセス間通信が必要

### Phase 3: 移行実装（4-8週間）
- 選択した代替案のプロトタイプ作成
- 既存機能の移植
- テストケースの作成
- 段階的な移行

### Phase 4: 本格移行（2-4週間）
- 本番環境での動作確認
- パフォーマンス比較
- ドキュメント更新

## 技術的な考慮事項

### 現在のBrowserUp Proxy使用箇所
1. **DriverTool.java** - メインのプロキシ制御
2. **SearchBySeller.java** - セラー検索でのHAR収集
3. **SearchByKeyWord.java** - キーワード検索でのHAR収集
4. **SellerListAnalyze.java** - セラー分析でのデータ収集

### 必要な機能
- HTTPリクエスト/レスポンスのキャプチャ
- HARフォーマットでのデータエクスポート
- リクエストヘッダーの変更（zstd除去など）
- SSL/TLS証明書の処理

### 移行時の注意点
- 既存のHARデータ形式との互換性
- Seleniumとの統合方法
- パフォーマンスへの影響
- 設定の移行

## リスク評価

### 高リスク
- **機能の互換性**: 新しいライブラリで同等の機能が提供されない可能性
- **パフォーマンス**: 移行により処理速度が低下する可能性

### 中リスク
- **開発工数**: 移行に予想以上の時間がかかる可能性
- **テスト**: 既存のテストケースの大幅な修正が必要

### 低リスク
- **学習コスト**: 新しいライブラリの習得時間
- **ドキュメント**: 移行に伴うドキュメント更新

## 推奨アクション

### 短期（即座）
1. ✅ バージョン更新の適用
2. ✅ リソース管理の改善
3. 🔄 動作確認とモニタリング

### 中期（1-3ヶ月）
1. mitmproxyの詳細調査
2. プロトタイプの作成
3. 移行計画の詳細化

### 長期（3-6ヶ月）
1. 段階的な移行実施
2. 本番環境での検証
3. 完全移行の完了

## 結論

現在のByteBufリソースリークは、バージョン更新とリソース管理の改善により大幅に軽減されるはずです。しかし、BrowserUp Proxyの非推奨化を考慮すると、中長期的にはmitmproxyへの移行を検討することを強く推奨します。

移行は段階的に行い、まずは現在のバージョン更新で安定性を確保してから、次のステップを計画することが重要です。
